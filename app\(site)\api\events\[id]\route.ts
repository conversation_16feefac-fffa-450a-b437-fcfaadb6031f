import { getEventById } from "@/app/(site)/event/controller";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { ApiHandlerProps, withApiKeyAuth } from "@/lib/auth";

const getSchema = z.object({
  id: z.string(),
});

const getHandler: ApiHandlerProps<{ id: string }> = async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) => {
  try {
    const { success, data } = await getSchema.safeParse(await params);
    if (!success) {
      return NextResponse.json({ error: "ID é obrigatório" }, { status: 400 });
    }
    const event = await getEventById(data.id);

    if (!event) {
      return NextResponse.json(
        { error: "Evento não encontrado" },
        { status: 404 },
      );
    }

    return NextResponse.json({ data: event });
  } catch (error) {
    console.error("Event ID API Error:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 },
    );
  }
};

export const GET = withApiKeyAuth(getHandler);
