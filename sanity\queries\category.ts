import { defineQuery } from "next-sanity";

const categoryField = `
  _id,
  _type,
  _createdAt,
  _updatedAt,
  name,
  slug,
  description,
  type,
  createdBy
`;

const fullCategoryFields = `
  _id,
  _type,
  _createdAt,
  _updatedAt,
  name,
  slug,
  description,
  type,
  events[]-> {
    _id,
    name,
    slug,
    startAt,
    endAt,
    location
  },
  createdBy
`;

export const getAllCategoriesQuery = defineQuery(
  `*[_type == "category" && defined(slug.current)] {
    ${categoryField}
  }`,
);

export const getCategoryBySlugQuery = defineQuery(`
  *[_type == "category" && slug.current == $slug][0] {
    ${fullCategoryFields}
  }
`);
