import { NextRequest, NextResponse } from "next/server";
import { getEventsCategories } from "@/app/(site)/event/controller";
import { withApiKeyAuth } from "@/lib/auth";

async function getHandler(request: NextRequest) {
  try {
    const categories = await getEventsCategories();

    if (!categories) {
      return NextResponse.json(
        { error: "Falha ao buscar categorias de eventos" },
        { status: 500 },
      );
    }

    return NextResponse.json({ data: categories });
  } catch (error) {
    console.error("Event Categories API Error:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 },
    );
  }
}

export const GET = withApi<PERSON>eyAuth(getHandler);
