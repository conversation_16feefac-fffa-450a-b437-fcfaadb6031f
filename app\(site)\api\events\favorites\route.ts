import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import {
  toggleFavoriteEvent,
  getUserFavoriteEvents,
  hasUserFavorited,
} from "@/app/(site)/event/controller/favorite";
import { withApiKeyAuth } from "@/lib/auth";

const favoriteSchema = z.object({
  userId: z.string(),
  eventId: z.string(),
});

async function postHandler(request: NextRequest) {
  try {
    const schema = await favoriteSchema.safeParse(await request.json());
    if (!schema.success) {
      return NextResponse.json(
        { error: "UserId e EventId são obrigatórios" },
        { status: 400 },
      );
    }
    const { userId, eventId } = schema.data;

    await toggleFavoriteEvent({ userId, eventId });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Favorite Toggle API Error:", error);
    return NextResponse.json(
      { error: "Falha ao alternar favorito" },
      { status: 500 },
    );
  }
}

const getSchema = z.object({
  userId: z.string(),
  eventId: z.string().optional(),
});

async function getHandler(request: NextRequest) {
  try {
    const { success, data } = await getSchema.safeParse(
      Object.fromEntries(request.nextUrl.searchParams.entries()),
    );
    if (!success) {
      return NextResponse.json(
        { error: "UserId é obrigatório" },
        { status: 400 },
      );
    }
    const { userId, eventId } = data;

    if (!eventId) {
      const events = await getUserFavoriteEvents(userId);

      if (!events) {
        return NextResponse.json(
          { error: "Falha ao buscar eventos favoritos" },
          { status: 500 },
        );
      }
      return NextResponse.json({ data: events });
    }

    const hasFavorited = await hasUserFavorited({
      userId,
      eventId,
    });
    return NextResponse.json({ data: hasFavorited });
  } catch (error) {
    console.error("Get Favorites API Error:", error);
    return NextResponse.json(
      { error: "Falha ao obter favoritos" },
      { status: 500 },
    );
  }
}

export const POST = withApiKeyAuth(postHandler);
export const GET = withApiKeyAuth(getHandler);
