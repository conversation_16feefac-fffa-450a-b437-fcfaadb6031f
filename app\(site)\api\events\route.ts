import { NextRequest, NextResponse } from "next/server";
import { getAllEvents } from "@/app/(site)/event/controller";
import { withApiKeyAuth } from "@/lib/auth";

async function getHandler(request: NextRequest) {
  try {
    const events = await getAllEvents(
      Object.fromEntries(request.nextUrl.searchParams.entries()),
    );

    if (!events) {
      return NextResponse.json(
        { error: "Falha ao buscar eventos" },
        { status: 500 },
      );
    }

    return NextResponse.json({ data: events });
  } catch (error) {
    console.error("Events API Error:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 },
    );
  }
}

export const GET = withApiKeyAuth(getHandler);
