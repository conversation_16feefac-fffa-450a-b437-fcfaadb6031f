import { CATEGORY_TYPES } from "@/app/(site)/category/schema";
import { TagIcon } from "@sanity/icons";
import { defineField, defineType } from "sanity";

export const categoryType = defineType({
  name: "category",
  title: "Categorias",
  type: "document",
  icon: TagIcon,
  fields: [
    defineField({
      name: "name",
      title: "Nome",
      type: "string",
      validation: (rule) => rule.max(30).required(),
    }),
    defineField({
      name: "slug",
      type: "slug",
      options: {
        source: "name",
      },
    }),
    defineField({
      name: "description",
      title: "Descrição",
      type: "text",
      validation: (rule) => rule.max(500).required(),
    }),
    defineField({
      name: "type",
      title: "Tipo",
      type: "string",
      options: {
        list: Object.entries(CATEGORY_TYPES).map(([key, value]) => ({
          title: value,
          value: key,
        })),
      },
      initialValue: "NORMAL",
    }),
    defineField({
      name: "events",
      title: "Eventos",
      type: "array",
      of: [{ type: "reference", to: { type: "event" } }],
    }),
    defineField({
      name: "createdBy",
      title: "Criado por",
      type: "string",
      validation: (rule) => rule.max(50).required(),
    }),
  ],
  preview: {
    select: {
      title: "name",
      subtitle: "type",
    },
  },
});
