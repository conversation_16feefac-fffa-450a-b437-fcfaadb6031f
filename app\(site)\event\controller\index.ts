import { client } from "@/sanity/lib/client";
import {
  getAllEventsQuery,
  getEventBySlugQuery,
  getEventLocationsQuery,
  getEventOrganizersQuery,
  getEventCategoriesQuery,
  getEventByIdQuery,
} from "@/sanity/queries/event";
import { z } from "zod";
import {
  eventSchema,
  EventFiltersType,
  eventFiltersSchema,
  eventCategorySchema,
} from "../schema";

export const getAllEvents = async (filters?: EventFiltersType) => {
  let validatedFilters: EventFiltersType = {};

  if (filters) {
    const parsedFilters = eventFiltersSchema.safeParse(filters);
    if (parsedFilters.success) {
      validatedFilters = parsedFilters.data;
    } else {
      console.error("Invalid filters:", parsedFilters.error);
      return null;
    }
  }

  const events = await client
    .fetch(getAllEventsQuery(validatedFilters))
    .catch((err) => {
      console.error(err);
      return null;
    });

  if (!events) {
    return null;
  }

  const schema = z.array(eventSchema).safeParse(events);

  if (!schema.success) {
    console.error(schema.error);
    return null;
  }

  return schema.data;
};

export const getEventBySlug = async (slug: string) => {
  const event = await client
    .fetch(getEventBySlugQuery, { slug })
    .catch((err) => {
      console.error(err);
      return null;
    });

  const schema = eventSchema.safeParse(event);

  if (!schema.success) {
    console.error(schema.error);
    return null;
  }

  return schema.data;
};

export const getEventById = async (id: string) => {
  const event = await client.fetch(getEventByIdQuery, { id }).catch((err) => {
    console.error(err);
    return null;
  });

  const schema = eventSchema.safeParse(event);

  if (!schema.success) {
    console.error(schema.error);
    return null;
  }

  return schema.data;
};

type LocationType = {
  name: string;
};

export const getEventsLocations = async (): Promise<LocationType[] | null> => {
  const locations = await client.fetch(getEventLocationsQuery).catch((err) => {
    console.error("Error fetching event locations:", err);
    return null;
  });

  if (!locations) {
    return null;
  }

  return z.array(z.object({ name: z.string() })).parse(locations);
};

type OrganizerType = {
  name: string;
};

export const getEventsOrganizers = async (): Promise<
  OrganizerType[] | null
> => {
  const organizers = await client
    .fetch(getEventOrganizersQuery)
    .catch((err) => {
      console.error("Error fetching event organizers:", err);
      return null;
    });

  if (!organizers) {
    return null;
  }

  return z.array(z.object({ name: z.string() })).parse(organizers);
};

export const getEventsCategories = async () => {
  const categories = await client
    .fetch(getEventCategoriesQuery)
    .catch((err) => {
      console.error("Error fetching event categories:", err);
      return null;
    });

  if (!categories) {
    return null;
  }

  return z.array(eventCategorySchema).parse(categories);
};
