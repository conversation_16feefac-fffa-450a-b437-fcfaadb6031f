{"name": "zimbora-web", "version": "0.8.0", "private": true, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "scripts": {"ngrok": "ngrok http 3000", "ts:lint": "tsc", "email": "ts-node scripts/email-envs.ts", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "npx prisma generate", "test": "vitest run", "sanity:seed": "node --experimental-strip-types ./sanity/seed-ndjson.ts && sanity dataset import seed.ndjson dev"}, "dependencies": {"@better-auth/expo": "^1.1.21", "@clerk/nextjs": "^6.21.0", "@headlessui/react": "^1.7.13", "@heroicons/react": "^2.0.16", "@prisma/client": "^6.4.1", "@radix-ui/react-popover": "^1.1.11", "@sanity/icons": "^3.7.0", "@sanity/image-url": "1", "@sanity/vision": "^3.86.1", "@workattackdev/wdk": "^0.4.5", "axios": "^1.3.4", "class-variance-authority": "^0.7.1", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "cookies-next": "^2.1.1", "cors": "^2.8.5", "expo-server-sdk": "^3.15.0", "jsonwebtoken": "^9.0.0", "lucide-react": "^0.475.0", "next": "15.3.3", "next-connect": "^0.13.0", "next-sanity": "^9.12.0", "nodemailer": "^6.10.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.56.1", "react-query": "^3.39.3", "sanity": "^3.86.1", "styled-components": "6", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.3", "zustand": "4.3.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@faker-js/faker": "^9.5.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/line-clamp": "^0.4.2", "@tailwindcss/postcss": "^4.1.8", "@types/bcrypt": "^5.0.0", "@types/cors": "^2.8.13", "@types/jsonwebtoken": "^9.0.1", "@types/multer": "^1.4.7", "@types/node": "22.15.1", "@types/nodemailer": "^6.4.7", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "autoprefixer": "^10.4.21", "bcrypt": "^5.1.0", "eslint": "9.25.1", "eslint-config-next": "15.3.3", "postcss": "^8.5.4", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "prisma": "^6.4.1", "sass": "^1.58.3", "tailwindcss": "^4.1.8", "ts-node": "^10.9.2", "typescript": "5.8.3", "vitest": "^3.0.6"}, "pnpm": {"onlyBuiltDependencies": ["@clerk/shared", "@parcel/watcher", "@prisma/client", "@prisma/engines", "@tailwindcss/oxide", "bcrypt", "core-js", "esbuild", "prisma", "sharp", "unrs-resolver"], "overrides": {"@types/react": "19.0.10", "@types/react-dom": "19.0.4"}}}