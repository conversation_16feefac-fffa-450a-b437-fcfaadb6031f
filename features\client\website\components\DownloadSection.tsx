import Image from "next/image";
import AppDownloadButtons from "@/components/ui/app-download-buttons";

const DownloadSection = () => {
  return (
    <section
      id="download"
      className="from-primary1 to-primary2 flex max-h-[30rem] scroll-mt-40 flex-col items-center space-y-4 overflow-hidden rounded-lg bg-gradient-to-r p-4 shadow sm:max-h-[15rem] sm:flex-row sm:p-6 md:justify-between md:rounded-xl"
    >
      <article className="flex-col space-y-4 md:w-3/5">
        <h5 className="text-2xl font-semibold text-white">
          Participe de eventos com facilidade
        </h5>
        <p className="text-light-primary text-sm">
          Baixe o aplicativo Zimbora na Play Store ou App Store e participe dos
          melhores eventos que temos para você.
        </p>
        <div className="flex md:justify-start">
          <AppDownloadButtons variant="badges" size="md" className="gap-1" />
        </div>
      </article>
      <Image
        src="/images/phones2.png"
        alt="two phones"
        width={720}
        height={1080}
        className="max-h-[25rem] w-full object-contain md:w-1/2 md:translate-y-20"
      />
    </section>
  );
};

export default DownloadSection;
