import { client } from "@/sanity/lib/client";
import {
  getAllCategoriesQuery,
  getCategoryBySlugQuery,
} from "@/sanity/queries/category";
import { z } from "zod";
import { categorySchema } from "../schema";

export const getAllCategories = async () => {
  const categories = await client.fetch(getAllCategoriesQuery).catch((err) => {
    console.error(err);
    return null;
  });

  const schema = z.array(categorySchema).safeParse(categories);

  if (!schema.success) {
    console.error(schema.error);
    return null;
  }

  return schema.data;
};

export const getCategoryBySlug = async (slug: string) => {
  const category = await client
    .fetch(getCategoryBySlugQuery, { slug })
    .catch((err) => {
      console.error(err);
      return null;
    });

  const schema = categorySchema.safeParse(category);

  if (!schema.success) {
    console.error(schema.error);
    return null;
  }

  return schema.data;
};
