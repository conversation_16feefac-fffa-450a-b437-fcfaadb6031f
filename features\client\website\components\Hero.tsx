import { cn } from "@/lib/utils";
import Image from "next/image";
import Link from "next/link";
import Button from "../../core/components/Button";

type Props = {
  className?: string;
};

const Hero = ({ className }: Props) => {
  return (
    <section
      className={cn(
        "mx-auto mt-9 flex flex-col items-center p-6 md:flex-row md:p-8",
        className,
      )}
    >
      <article className="relative flex h-full min-w-[40%] flex-col justify-center pb-6 text-center md:text-left">
        <h1 className="text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
          <span className="text-secondary2 block xl:inline">
            Zimbora para os
          </span>{" "}
          <span className="block xl:inline">Melhores eventos de Luanda</span>
        </h1>
        <p className="mt-3 text-base text-gray-500 sm:mx-auto sm:mt-5 sm:max-w-xl sm:text-lg md:mt-5 lg:mx-0 lg:text-xl">
          Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem
          cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat
          aliqua.
        </p>
        <div className="mt-5 flex justify-center sm:mt-8 md:justify-start">
          <Link href="#download">
            <Button className="mr-4 !text-sm">Baixar APP</Button>
          </Link>
          <Link href="#product">
            <Button theme="secondary" className="!text-sm">
              Explorar
            </Button>
          </Link>
        </div>
        <span className="shadow-primary2/20 absolute bottom-0 block h-2 w-full p-2 shadow-xl md:hidden"></span>
      </article>
      <picture className="w-full flex-shrink-0 sm:w-3/4 md:-mt-10 md:w-1/2">
        <Image
          src="/images/phones.png"
          alt="phone image"
          width={500}
          height={1080}
          className="w-full object-contain"
        />
      </picture>
    </section>
  );
};

export default Hero;
