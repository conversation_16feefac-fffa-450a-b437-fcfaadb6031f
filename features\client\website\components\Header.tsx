"use client";
import {
  Pop<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Menu, X } from "lucide-react";
import Image from "next/image";
import Button from "../../core/components/Button";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { useState } from "react";

const navigation = [
  { name: "Produto", href: "#product" },
  { name: "Funcionalidades", href: "#features" },
  { name: "Empresa", href: "#" },
  { name: "Baixar", href: "#download" },
];

const Header = () => {
  const [open, setOpen] = useState(false);

  return (
    <header className="w-full">
      <div className="mx-auto">
        <div className="lg:w-full">
          <Popover open={open} onOpenChange={setOpen}>
            <nav
              className="fixed top-0 left-0 z-40 flex w-full items-center justify-between bg-white p-4 shadow md:px-6"
              aria-label="Global"
            >
              <div className="flex flex-shrink-0 flex-grow items-center lg:flex-grow-0">
                <div className="flex w-full items-center justify-between md:w-auto">
                  <a href="/">
                    <span className="sr-only">Zimbora</span>
                    <Image
                      src="/icons/logo_icon_h.svg"
                      width={150}
                      height={40}
                      className="object-contain"
                      alt="Zimbora logotipo"
                    />
                  </a>
                  <div className="-mr-2 flex items-center md:hidden">
                    <PopoverTrigger
                      className={cn(
                        "text-primary1 hover:bg-light-primary hover:text-primary2 focus:ring-primary2",
                        "inline-flex items-center justify-center rounded-md bg-white p-2",
                        "focus:ring-2 focus:outline-none focus:ring-inset",
                      )}
                    >
                      <span className="sr-only">Abrir Menu</span>
                      <Menu className="h-6 w-6" aria-hidden="true" />
                    </PopoverTrigger>
                  </div>
                </div>
              </div>
              <div className="hidden md:ml-10 md:block md:space-x-8 md:pr-4">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="text-light-secondary hover:text-dark-primary font-medium"
                  >
                    {item.name}
                  </Link>
                ))}
              </div>
            </nav>

            <PopoverContent className="md:hidden">
              <div className="ring-dark-primary ring-opacity-5 overflow-hidden rounded-lg bg-white ring-1 shadow-md">
                <div className="flex items-center justify-between px-5 pt-4">
                  <div>
                    <Image
                      width={32}
                      height={32}
                      className="h-8 w-auto"
                      src="/icons/logo_icon.svg"
                      alt="Zimbora icon"
                    />
                  </div>
                  <div className="-mr-2">
                    <button
                      onClick={() => setOpen(false)}
                      className={cn(
                        "text-primary1 hover:bg-light-primary hover:text-primary2 focus:ring-primary2",
                        "inline-flex items-center justify-center rounded-md bg-white p-2",
                        "focus:ring-2 focus:outline-none focus:ring-inset",
                      )}
                    >
                      <span className="sr-only">Close main menu</span>
                      <X className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>
                </div>
                <div className="space-y-1 px-2 pt-2 pb-3">
                  {navigation.map((item) => (
                    <a
                      key={item.name}
                      href={item.href}
                      className="block rounded-md px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                      onClick={() => setOpen(false)}
                    >
                      {item.name}
                    </a>
                  ))}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </header>
  );
};

export default Header;
